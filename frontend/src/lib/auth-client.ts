import { createAuthClient } from "better-auth/react";

const authClient: any = createAuthClient({
  baseURL: "http://localhost:3001",
  basePath: "/api/better-auth",
});

export { authClient };

// Export specific methods for convenience
export const signIn: any = authClient.signIn;
export const signUp: any = authClient.signUp;
export const signOut: any = authClient.signOut;
export const useSession: any = authClient.useSession;
export const getSession: any = authClient.getSession;
