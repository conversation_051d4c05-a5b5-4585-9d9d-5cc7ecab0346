/*! sprintf-js v1.1.3 | Copyright (c) 2007-present, <PERSON><PERSON><PERSON> <<EMAIL>> | BSD-3-Clause */
!function(){"use strict";angular.module("sprintf",[]).filter("sprintf",function(){return function(){return sprintf.apply(null,arguments)}}).filter("fmt",["$filter",function(t){return t("sprintf")}]).filter("vsprintf",function(){return function(t,n){return vsprintf(t,n)}}).filter("vfmt",["$filter",function(t){return t("vsprintf")}])}();
//# sourceMappingURL=angular-sprintf.min.js.map
