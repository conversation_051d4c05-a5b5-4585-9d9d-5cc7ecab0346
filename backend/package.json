{"name": "bookmarked-backend", "version": "1.0.0", "description": "Backend API for Bookmarked application", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon src/server.ts", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts"}, "dependencies": {"@types/better-sqlite3": "^7.6.13", "bcryptjs": "^2.4.3", "better-auth": "^1.2.12", "better-sqlite3": "^12.2.0", "bookmarked-types": "file:../packages/bookmarked-types", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "kysely": "^0.28.2", "mongodb": "^6.17.0", "mongoose": "^8.0.3", "morgan": "^1.10.0", "nodemailer": "^6.9.7", "sqlite3": "^5.1.7", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@types/nodemailer": "^6.4.14", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "keywords": ["api", "backend", "express", "typescript", "mongodb", "bookmarked"], "author": "<PERSON><PERSON>", "license": "MIT"}